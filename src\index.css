@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import 'react-flagpack/dist/style.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    /* Modern light theme for Net Zero platform */
    --font-family: 'Inter', sans-serif;
    
    /* Light backgrounds */
    --background: 0 0% 98%;         /* Almost white background */
    --foreground: 215 25% 27%;      /* Dark blue-grey text */
    
    --card: 0 0% 100%;              /* White for cards */
    --card-foreground: 215 25% 27%; /* Same dark text */
    
    --popover: 0 0% 100%;           /* White for popovers */
    --popover-foreground: 215 25% 27%;
    
    /* Green primary colors for sustainability theme */
    --primary: 152 72% 35%;         /* Rich forest green */
    --primary-foreground: 0 0% 100%; /* White text on green */
    
    --secondary: 162 55% 95%;       /* Very light green */
    --secondary-foreground: 152 72% 28%;

    --muted: 210 15% 95%;           /* Light grey for muted elements */
    --muted-foreground: 215 25% 40%;
    
    --accent: 162 55% 90%;          /* Light green accent */
    --accent-foreground: 152 72% 28%;
    
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    
    --border: 215 15% 90%;          /* Light border */
    --input: 215 15% 90%;
    --ring: 152 72% 35%;
    
    --radius: 0.5rem;
    
    /* Sidebar variables */
    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 215 25% 27%;
    --sidebar-primary: 152 72% 35%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 152 72% 85%;
    --sidebar-accent-foreground: 152 72% 28%;
    --sidebar-border: 215 15% 90%;
    --sidebar-ring: 152 72% 35%;
  }
  
  .dark {
    /* Dark mode theme */
    --background: 215 32% 17%;      /* Dark blue-grey background */
    --foreground: 214 15% 91%;      /* Light grey text */
    
    --card: 215 28% 23%;            /* Slightly lighter cards */
    --card-foreground: 214 15% 91%;
    
    --popover: 215 28% 23%;
    --popover-foreground: 214 15% 91%;
    
    /* Green primary colors for sustainability theme */
    --primary: 152 72% 45%;         /* Brighter green for dark mode */
    --primary-foreground: 0 0% 100%;
    
    --secondary: 215 25% 27%;
    --secondary-foreground: 210 15% 90%;
    
    --muted: 215 28% 26%;
    --muted-foreground: 214 5% 65%;
    
    --accent: 215 28% 26%;
    --accent-foreground: 214 15% 91%;
    
    --destructive: 0 62% 30%;
    --destructive-foreground: 0 0% 100%;
    
    --border: 215 28% 30%;
    --input: 215 28% 30%;
    --ring: 152 72% 55%;

    /* Sidebar variables */
    --sidebar-background: 215 32% 17%;
    --sidebar-foreground: 214 15% 91%;
    --sidebar-primary: 152 72% 45%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 28% 26%;
    --sidebar-accent-foreground: 214 15% 91%;
    --sidebar-border: 215 28% 30%;
    --sidebar-ring: 152 72% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-family);
    font-feature-settings: "rlig" 1, "calt" 1;
    /* Prevent horizontal scroll */
    overflow-x: hidden;
  }
  
  /* Prevent horizontal overflow */
  html, body {
    max-width: 100vw;
    overflow-x: hidden;
  }
  
  /* Ensure all containers respect max width */
  .container {
    max-width: 100%;
    box-sizing: border-box;
  }
}

/* Custom utilities for profile sidebar */
@layer utilities {
  .truncate-multiline {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-wrap: break-word;
    hyphens: auto;
  }
  
  .word-wrap {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
  }
  
  /* Responsive utilities for mobile */
  .mobile-scroll {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
  
  .mobile-text-size {
    @apply text-sm sm:text-base;
  }
  
  .mobile-padding {
    @apply p-3 sm:p-4;
  }
  
  .mobile-gap {
    @apply gap-2 sm:gap-3;
  }
  
  /* Prevent text overflow on all screen sizes */
  .safe-text {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    hyphens: auto;
  }
  
  /* Force SVG fills for reaction icons using CSS variables */
  .force-fill-blue {
    --reaction-fill: #2563eb;
    --reaction-stroke: #2563eb;
  }
  
  .force-fill-green {
    --reaction-fill: #16a34a;
    --reaction-stroke: #16a34a;
  }
  
  .force-fill-red {
    --reaction-fill: #dc2626;
    --reaction-stroke: #dc2626;
  }
  
  .force-fill-yellow {
    --reaction-fill: #ca8a04;
    --reaction-stroke: #ca8a04;
  }
  
  /* Apply the variables to all SVG elements */
  .force-fill-blue svg *,
  .force-fill-green svg *,
  .force-fill-red svg *,
  .force-fill-yellow svg * {
    fill: var(--reaction-fill) !important;
    stroke: var(--reaction-stroke) !important;
  }

  /* Additional targeting for SVG elements themselves */
  .force-fill-blue svg,
  .force-fill-green svg,
  .force-fill-red svg,
  .force-fill-yellow svg {
    fill: var(--reaction-fill) !important;
    stroke: var(--reaction-stroke) !important;
  }

  /* Additional targeting for Lucide React icons */
  .force-fill-blue [data-lucide],
  .force-fill-green [data-lucide],
  .force-fill-red [data-lucide],
  .force-fill-yellow [data-lucide] {
    fill: var(--reaction-fill) !important;
    stroke: var(--reaction-stroke) !important;
  }

  .force-fill-blue [data-lucide] *,
  .force-fill-green [data-lucide] *,
  .force-fill-red [data-lucide] *,
  .force-fill-yellow [data-lucide] * {
    fill: var(--reaction-fill) !important;
    stroke: var(--reaction-stroke) !important;
  }
}