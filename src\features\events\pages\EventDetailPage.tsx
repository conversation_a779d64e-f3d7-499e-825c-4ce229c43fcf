import { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { 
  ArrowLeft, 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Heart, 
  ExternalLink,
  Globe,
  Building,
  Monitor,
  Mail,
  Phone,
  Edit,
  Trash2,
  UserCheck,
  Shield
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/integrations/supabase/client'
import { EventService } from '../services/eventService'
import type { EventWithInterests, EventInterestFormData, AttendeeProfile } from '../types'
import { EVENT_LOCATION_TYPES } from '../types'
import AddToCalendarDropdown from '../components/AddToCalendarDropdown'

const EventDetailPage = () => {
  const { eventId } = useParams<{ eventId: string }>()
  const navigate = useNavigate()
  const { user } = useAuth()
  const { toast } = useToast()
  
  const [event, setEvent] = useState<EventWithInterests | null>(null)
  const [attendees, setAttendees] = useState<AttendeeProfile[]>([])
  const [publicAttendees, setPublicAttendees] = useState<AttendeeProfile[]>([])
  const [loading, setLoading] = useState(true)
  const [isUpdatingInterest, setIsUpdatingInterest] = useState(false)
  const [showInterestDialog, setShowInterestDialog] = useState(false)
  const [showAttendeesDialog, setShowAttendeesDialog] = useState(false)
  const [showPublicAttendeesDialog, setShowPublicAttendeesDialog] = useState(false)
  
  // Interest form state
  const [isInterested, setIsInterested] = useState(false)
  const [isAttending, setIsAttending] = useState(false)
  const [gdprConsent, setGdprConsent] = useState(false)
  const [visibilityConsent, setVisibilityConsent] = useState(false)
  const [note, setNote] = useState('')

  useEffect(() => {
    if (!eventId) return
    loadEventDetails()
  }, [eventId])

  const loadEventDetails = async () => {
    if (!eventId) return
    
    try {
      setLoading(true)
      const eventData = await EventService.getEventById(eventId)
      
      if (!eventData) {
        toast({
          title: "Event not found",
          description: "The event you're looking for doesn't exist.",
          variant: "destructive"
        })
        navigate('/events')
        return
      }

      setEvent(eventData)

      // Load user's existing interest if logged in
      if (user) {
        const userInterest = await EventService.getUserInterest(eventId, user.id)
        if (userInterest) {
          setIsInterested(userInterest.is_interested || false)
          setIsAttending(userInterest.is_attending || false)
          setGdprConsent(userInterest.gdpr_consent_given || false)
          setVisibilityConsent(userInterest.visibility_consent_given || false)
          setNote(userInterest.note || '')
          eventData.user_interest = userInterest
        }
      }

      // Load attendees if user is the organizer
      if (user && eventData.created_by_user_id === user.id) {
        try {
          const attendeeData = await EventService.getEventAttendees(eventId)
          setAttendees(attendeeData)
        } catch (error) {
          console.error('Error loading attendees:', error)
        }
      }

      // Load publicly visible attendees for all users
      try {
        const publicAttendeeData = await EventService.getPublicAttendees(eventId)
        setPublicAttendees(publicAttendeeData)
      } catch (error) {
        console.error('Error loading public attendees:', error)
      }

    } catch (error) {
      console.error('Error loading event:', error)
      toast({
        title: "Error",
        description: "Failed to load event details",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const checkUserProfileVisibility = async (): Promise<boolean> => {
    if (!user) return false
    
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('profile_visible')
        .eq('id', user.id)
        .single()
      
      if (error) {
        console.error('Error checking profile visibility:', error)
        return false
      }
      
      return data?.profile_visible || false
    } catch (error) {
      console.error('Error checking profile visibility:', error)
      return false
    }
  }

  const handleInterestSubmit = async () => {
    if (!eventId || !user) return

    setIsUpdatingInterest(true)
    
    try {
      const interestData: Omit<EventInterestFormData, 'note'> & { note?: string } = {
        is_interested: isInterested,
        is_attending: isAttending,
        gdpr_consent_given: gdprConsent,
        visibility_consent_given: visibilityConsent,
        note: note.trim() || undefined
      }

      await EventService.expressInterest(eventId, interestData)
      
      // Check if user has given visibility consent but profile is not visible
      if (visibilityConsent) {
        const isProfileVisible = await checkUserProfileVisibility()
        if (!isProfileVisible) {
          toast({
            title: "Visibility Consent Note",
            description: "You've consented to be visible to other users, but this will only work if your profile visibility is set to public in your profile settings.",
            variant: "default"
          })
        }
      }
      
      toast({
        title: "Success",
        description: isAttending
          ? "You're now registered for this event! Don't forget to add it to your calendar."
          : isInterested
            ? "Interest recorded successfully!"
            : "Your response has been updated."
      })
      
      setShowInterestDialog(false)
      loadEventDetails() // Reload to get updated counts
      
    } catch (error) {
      console.error('Error updating interest:', error)
      toast({
        title: "Error",
        description: "Failed to update your interest",
        variant: "destructive"
      })
    } finally {
      setIsUpdatingInterest(false)
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!event) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="py-12 text-center">
            <h3 className="text-lg font-semibold mb-2">Event not found</h3>
            <p className="text-muted-foreground mb-4">
              The event you're looking for doesn't exist or has been removed.
            </p>
            <Button onClick={() => navigate('/events')}>
              Back to Events
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Helper functions
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-GB', {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    })
  }

  const formatTime = (timeString: string | null) => {
    if (!timeString) return null
    return timeString.slice(0, 5) // Format HH:MM
  }

  const getLocationTypeInfo = () => {
    return EVENT_LOCATION_TYPES.find(type => type.value === event.location_type)
  }

  const getLocationIcon = () => {
    switch (event.location_type) {
      case 'online':
        return <Monitor className="h-4 w-4" />
      case 'face_to_face':
        return <Building className="h-4 w-4" />
      case 'hybrid':
        return <Globe className="h-4 w-4" />
      default:
        return <MapPin className="h-4 w-4" />
    }
  }

  const isEventPast = () => {
    const eventDate = new Date(event.start_date)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return eventDate < today
  }

  const isOrganizer = user && event.created_by_user_id === user.id
  const locationTypeInfo = getLocationTypeInfo()

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      {/* Header */}
      <div className="mb-6">
        <Button 
          variant="ghost" 
          onClick={() => navigate('/events')}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Events
        </Button>

        {/* Mobile-first responsive header */}
        <div className="space-y-4">
          <div className="space-y-2">
            <h1 className="text-2xl sm:text-3xl font-bold leading-tight">{event.name}</h1>
            <p className="text-lg sm:text-xl text-muted-foreground">
              Organized by {event.organizer_name}
            </p>
            
            <div className="flex items-center gap-2">
              <Badge 
                variant={isEventPast() ? "secondary" : "default"}
                className="flex items-center gap-1"
              >
                {getLocationIcon()}
                {locationTypeInfo?.label || event.location_type}
              </Badge>
            </div>
          </div>
          
          {isOrganizer && (
            <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
              <Button variant="outline" size="sm" className="w-full sm:w-auto">
                <Edit className="h-3 w-3 mr-1" />
                Edit
              </Button>
              <Button variant="outline" size="sm" className="w-full sm:w-auto">
                <Trash2 className="h-3 w-3 mr-1" />
                Delete
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Event Details */}
          <Card>
            <CardHeader>
              <CardTitle>Event Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Date and Time */}
              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <p className="font-medium">{formatDate(event.start_date)}</p>
                    {event.end_date && event.end_date !== event.start_date && (
                      <p className="text-sm text-muted-foreground">
                        to {formatDate(event.end_date)}
                      </p>
                    )}
                  </div>
                </div>
                
                {(event.start_time || event.end_time) && (
                  <div className="flex items-center gap-3">
                    <Clock className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">
                        {formatTime(event.start_time)}
                        {event.end_time && ` - ${formatTime(event.end_time)}`}
                      </p>
                      {event.timezone && (
                        <p className="text-sm text-muted-foreground">{event.timezone}</p>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Location */}
              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div className="space-y-1">
                  {event.location_type === 'online' ? (
                    <div>
                      <p className="font-medium">Online Event</p>
                      {event.online_meeting_url && (
                        <Button
                          variant="link"
                          className="p-0 h-auto"
                          onClick={() => window.open(event.online_meeting_url!, '_blank')}
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          Join Meeting
                        </Button>
                      )}
                      {event.online_meeting_details && (
                        <p className="text-sm text-muted-foreground mt-1">
                          {event.online_meeting_details}
                        </p>
                      )}
                    </div>
                  ) : (
                    <div>
                      {event.venue_name && (
                        <p className="font-medium">{event.venue_name}</p>
                      )}
                      {event.address_line_1 && (
                        <p className="text-sm">{event.address_line_1}</p>
                      )}
                      {event.address_line_2 && (
                        <p className="text-sm">{event.address_line_2}</p>
                      )}
                      {(event.city || event.postcode) && (
                        <p className="text-sm">
                          {event.city}{event.city && event.postcode && ', '}{event.postcode}
                        </p>
                      )}
                      {event.location_type === 'hybrid' && event.online_meeting_url && (
                        <Button
                          variant="link"
                          className="p-0 h-auto mt-1"
                          onClick={() => window.open(event.online_meeting_url!, '_blank')}
                        >
                          <ExternalLink className="h-3 w-3 mr-1" />
                          Also join online
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Description */}
              {event.description && (
                <div>
                  <h4 className="font-medium mb-2">About this event</h4>
                  <p className="text-muted-foreground whitespace-pre-wrap">
                    {event.description}
                  </p>
                </div>
              )}

              {/* Tags */}
              {event.tags && event.tags.length > 0 && (
                <div>
                  <h4 className="font-medium mb-2">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {event.tags.map((tag, index) => (
                      <Badge key={index} variant="outline">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* External Links */}
              <div className="flex gap-2">
                {event.registration_url && (
                  <Button
                    onClick={() => window.open(event.registration_url!, '_blank')}
                    className="flex items-center gap-2"
                  >
                    <ExternalLink className="h-4 w-4" />
                    Register Now
                  </Button>
                )}
                
                {event.event_url && (
                  <Button
                    variant="outline"
                    onClick={() => window.open(event.event_url!, '_blank')}
                    className="flex items-center gap-2"
                  >
                    <ExternalLink className="h-4 w-4" />
                    Event Website
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Organizer Info */}
          <Card>
            <CardHeader>
              <CardTitle>Organizer</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-start gap-4">
                {event.creator && (
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={event.creator.avatar_url} />
                    <AvatarFallback>
                      {event.creator.first_name?.[0]}{event.creator.last_name?.[0]}
                    </AvatarFallback>
                  </Avatar>
                )}
                <div className="space-y-2">
                  <div>
                    <p className="font-medium">{event.organizer_name}</p>
                    {event.creator && (
                      <p className="text-sm text-muted-foreground">
                        {event.creator.first_name} {event.creator.last_name}
                      </p>
                    )}
                  </div>
                  
                  <div className="space-y-1">
                    {event.organizer_email && (
                      <div className="flex items-center gap-2 text-sm">
                        <Mail className="h-3 w-3" />
                        <a 
                          href={`mailto:${event.organizer_email}`}
                          className="text-primary hover:underline"
                        >
                          {event.organizer_email}
                        </a>
                      </div>
                    )}
                    
                    {event.organizer_phone && (
                      <div className="flex items-center gap-2 text-sm">
                        <Phone className="h-3 w-3" />
                        <a 
                          href={`tel:${event.organizer_phone}`}
                          className="text-primary hover:underline"
                        >
                          {event.organizer_phone}
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Event Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Event Stats
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Stats */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Interested</span>
                  <Badge variant="secondary">
                    <Heart className="h-3 w-3 mr-1" />
                    {event.interest_count || 0}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Attending</span>
                  <Badge variant="secondary">
                    <UserCheck className="h-3 w-3 mr-1" />
                    {event.attending_count || 0}
                  </Badge>
                </div>

                {event.max_attendees && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Max Attendees</span>
                    <span className="text-sm font-medium">{event.max_attendees}</span>
                  </div>
                )}
              </div>

              {/* Public Attendees */}
              {publicAttendees.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Public Attendees</span>
                    <Dialog open={showPublicAttendeesDialog} onOpenChange={setShowPublicAttendeesDialog}>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          View All ({publicAttendees.length})
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>Public Attendees</DialogTitle>
                          <DialogDescription>
                            Users who have registered for this event with public profiles and consented to be visible.
                          </DialogDescription>
                        </DialogHeader>

                        <div className="space-y-4 max-h-96 overflow-y-auto">
                          {publicAttendees.map((attendee) => (
                            <div key={attendee.id} className="flex items-center gap-3 p-3 border rounded-md">
                              <Avatar className="h-8 w-8">
                                <AvatarImage src={attendee.avatar_url} />
                                <AvatarFallback className="text-xs">
                                  {attendee.first_name?.[0]}{attendee.last_name?.[0]}
                                </AvatarFallback>
                              </Avatar>
                              <div className="flex-1">
                                <p className="font-medium text-sm">
                                  {attendee.first_name} {attendee.last_name}
                                </p>
                                {(attendee.job_title || attendee.organisation_name) && (
                                  <div className="text-xs text-muted-foreground mt-1">
                                    {attendee.job_title && (
                                      <div>{attendee.job_title}</div>
                                    )}
                                    {attendee.organisation_name && (
                                      <div>{attendee.organisation_name}</div>
                                    )}
                                  </div>
                                )}
                                <div className="flex gap-2 mt-1">
                                  {attendee.is_attending && (
                                    <Badge variant="outline" className="text-xs">
                                      <UserCheck className="h-2 w-2 mr-1" />
                                      Attending
                                    </Badge>
                                  )}
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>

                  {/* Show first few attendees */}
                  <div className="space-y-2">
                    {publicAttendees.slice(0, 3).map((attendee) => (
                      <div key={attendee.id} className="flex items-center gap-2">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={attendee.avatar_url} />
                          <AvatarFallback className="text-xs">
                            {attendee.first_name?.[0]}{attendee.last_name?.[0]}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <span className="text-sm font-medium">
                            {attendee.first_name} {attendee.last_name}
                          </span>
                          {attendee.job_title && (
                            <div className="text-xs text-muted-foreground truncate">
                              {attendee.job_title}
                            </div>
                          )}
                        </div>
                        <div className="flex gap-1">
                          {attendee.is_attending && (
                            <UserCheck className="h-3 w-3 text-green-500" />
                          )}
                        </div>
                      </div>
                    ))}
                    {publicAttendees.length > 3 && (
                      <p className="text-xs text-muted-foreground">
                        +{publicAttendees.length - 3} more registered
                      </p>
                    )}
                  </div>
                </div>
              )}

              {/* Calendar Integration */}
              <div className="space-y-2">
                <AddToCalendarDropdown
                  event={event}
                  variant="outline"
                  size="default"
                  className="w-full"
                />
              </div>

              {/* User Actions */}
              {user && !isEventPast() && (
                <div className="space-y-2">
                  <Dialog open={showInterestDialog} onOpenChange={setShowInterestDialog}>
                    <DialogTrigger asChild>
                      <Button className="w-full">
                        {event.user_interest?.is_attending
                          ? "Update Attendance"
                          : event.user_interest?.is_interested
                            ? "Update Interest"
                            : "Show Interest"
                        }
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Event Interest & Attendance</DialogTitle>
                        <DialogDescription>
                          Let the organizer know if you're interested or planning to attend.
                        </DialogDescription>
                      </DialogHeader>
                      
                      <div className="space-y-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="interested"
                            checked={isInterested}
                            onCheckedChange={(checked) => setIsInterested(checked === true)}
                          />
                          <label htmlFor="interested" className="text-sm font-medium">
                            I'm interested in this event
                          </label>
                        </div>

                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="attending"
                            checked={isAttending}
                            onCheckedChange={(checked) => setIsAttending(checked === true)}
                          />
                          <label htmlFor="attending" className="text-sm font-medium">
                            I plan to attend this event
                          </label>
                        </div>

                        {(isInterested || isAttending) && (
                          <>
                            <div className="flex items-start space-x-2 p-3 bg-blue-50 rounded-md">
                              <Shield className="h-4 w-4 text-blue-600 mt-0.5" />
                              <div className="space-y-4">
                                <div className="space-y-2">
                                  <div className="flex items-center space-x-2">
                                    <Checkbox
                                      id="gdpr"
                                      checked={gdprConsent}
                                      onCheckedChange={(checked) => setGdprConsent(checked === true)}
                                    />
                                    <label htmlFor="gdpr" className="text-xs font-medium">
                                      I consent to the organizer contacting me about this event
                                    </label>
                                  </div>
                                  <p className="text-xs text-muted-foreground">
                                    This allows the organizer to send you event updates and contact details.
                                    You can withdraw consent at any time.
                                  </p>
                                </div>

                                <div className="space-y-2">
                                  <div className="flex items-center space-x-2">
                                    <Checkbox
                                      id="visibility"
                                      checked={visibilityConsent}
                                      onCheckedChange={(checked) => setVisibilityConsent(checked === true)}
                                    />
                                    <label htmlFor="visibility" className="text-xs font-medium">
                                      I consent to other users seeing that I'm registered for this event
                                    </label>
                                  </div>
                                  <p className="text-xs text-muted-foreground">
                                    This allows other registered users to see your name and profile in the attendee list.
                                    You can change this at any time.
                                  </p>
                                </div>
                              </div>
                            </div>

                            <div>
                              <label className="text-sm font-medium">Optional note</label>
                              <Textarea
                                value={note}
                                onChange={(e) => setNote(e.target.value)}
                                placeholder="Any questions or comments for the organizer..."
                                rows={3}
                              />
                            </div>
                          </>
                        )}

                        <div className="flex gap-2">
                          <Button
                            onClick={handleInterestSubmit}
                            disabled={isUpdatingInterest || ((isInterested || isAttending) && !gdprConsent)}
                            className="flex-1"
                          >
                            {isUpdatingInterest ? 'Updating...' : 'Update Response'}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => setShowInterestDialog(false)}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              )}

              {/* Organizer Actions */}
              {isOrganizer && attendees.length > 0 && (
                <Dialog open={showAttendeesDialog} onOpenChange={setShowAttendeesDialog}>
                  <DialogTrigger asChild>
                    <Button variant="outline" className="w-full">
                      View Attendees ({attendees.length})
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Event Attendees</DialogTitle>
                      <DialogDescription>
                        People who have given consent to be contacted about this event.
                      </DialogDescription>
                    </DialogHeader>
                    
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {attendees.map((attendee) => (
                        <div key={attendee.id} className="flex items-center gap-3 p-3 border rounded-md">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={attendee.avatar_url} />
                            <AvatarFallback className="text-xs">
                              {attendee.first_name?.[0]}{attendee.last_name?.[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1">
                            <p className="font-medium text-sm">
                              {attendee.first_name} {attendee.last_name}
                            </p>
                            {(attendee.job_title || attendee.organisation_name) && (
                              <div className="text-xs text-muted-foreground mt-1">
                                {attendee.job_title && (
                                  <div>{attendee.job_title}</div>
                                )}
                                {attendee.organisation_name && (
                                  <div>{attendee.organisation_name}</div>
                                )}
                              </div>
                            )}
                            <div className="flex gap-2 mt-1">
                              {attendee.is_interested && (
                                <Badge variant="outline" className="text-xs">
                                  <Heart className="h-2 w-2 mr-1" />
                                  Interested
                                </Badge>
                              )}
                              {attendee.is_attending && (
                                <Badge variant="outline" className="text-xs">
                                  <UserCheck className="h-2 w-2 mr-1" />
                                  Attending
                                </Badge>
                              )}
                            </div>
                            {attendee.note && (
                              <p className="text-xs text-muted-foreground mt-1">
                                "{attendee.note}"
                              </p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </DialogContent>
                </Dialog>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default EventDetailPage
