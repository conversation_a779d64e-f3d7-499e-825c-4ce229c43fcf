import { Event, CalendarEvent, CalendarService } from '../types'

/**
 * Convert an Event object to a CalendarEvent format
 */
export function eventToCalendarEvent(event: Event): CalendarEvent {
  // Determine location based on event type
  let location = ''
  if (event.location_type === 'online' && event.online_meeting_url) {
    location = event.online_meeting_url
  } else if (event.location_type === 'face_to_face') {
    const locationParts = [
      event.venue_name,
      event.address_line_1,
      event.address_line_2,
      event.city,
      event.postcode
    ].filter(Boolean)
    location = locationParts.join(', ')
  } else if (event.location_type === 'hybrid') {
    const physicalParts = [
      event.venue_name,
      event.address_line_1,
      event.address_line_2,
      event.city,
      event.postcode
    ].filter(Boolean)
    const physicalLocation = physicalParts.join(', ')
    location = physicalLocation
    if (event.online_meeting_url) {
      location += ` | Online: ${event.online_meeting_url}`
    }
  }

  return {
    title: event.name,
    description: event.description || undefined,
    startDate: event.start_date,
    startTime: event.start_time || undefined,
    endDate: event.end_date || undefined,
    endTime: event.end_time || undefined,
    timezone: event.timezone || 'Europe/London',
    location: location || undefined,
    url: event.event_url || undefined
  }
}

/**
 * Format date and time for iCalendar format (YYYYMMDDTHHMMSSZ)
 */
export function formatDateTimeForICal(date: string, time?: string, timezone = 'Europe/London'): string {
  const dateObj = new Date(`${date}${time ? `T${time}` : 'T00:00'}`)
  
  // Convert to UTC for iCal format
  const utcDate = new Date(dateObj.toLocaleString('en-US', { timeZone: timezone }))
  
  const year = utcDate.getFullYear()
  const month = String(utcDate.getMonth() + 1).padStart(2, '0')
  const day = String(utcDate.getDate()).padStart(2, '0')
  const hours = String(utcDate.getHours()).padStart(2, '0')
  const minutes = String(utcDate.getMinutes()).padStart(2, '0')
  const seconds = String(utcDate.getSeconds()).padStart(2, '0')
  
  return `${year}${month}${day}T${hours}${minutes}${seconds}Z`
}

/**
 * Generate iCal file content
 */
export function generateICalContent(calendarEvent: CalendarEvent): string {
  const startDateTime = formatDateTimeForICal(
    calendarEvent.startDate, 
    calendarEvent.startTime, 
    calendarEvent.timezone
  )
  
  let endDateTime: string
  if (calendarEvent.endDate && calendarEvent.endTime) {
    endDateTime = formatDateTimeForICal(
      calendarEvent.endDate, 
      calendarEvent.endTime, 
      calendarEvent.timezone
    )
  } else if (calendarEvent.endDate) {
    endDateTime = formatDateTimeForICal(
      calendarEvent.endDate, 
      '23:59', 
      calendarEvent.timezone
    )
  } else {
    // Default to 1 hour duration if no end time specified
    const startDate = new Date(`${calendarEvent.startDate}T${calendarEvent.startTime || '00:00'}`)
    startDate.setHours(startDate.getHours() + 1)
    endDateTime = formatDateTimeForICal(
      startDate.toISOString().split('T')[0],
      startDate.toTimeString().slice(0, 5),
      calendarEvent.timezone
    )
  }

  const uid = `event-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  const timestamp = new Date().toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z'

  let icalContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Nexzero//Event Calendar//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:PUBLISH',
    'BEGIN:VEVENT',
    `UID:${uid}`,
    `DTSTAMP:${timestamp}`,
    `DTSTART:${startDateTime}`,
    `DTEND:${endDateTime}`,
    `SUMMARY:${calendarEvent.title}`,
  ]

  if (calendarEvent.description) {
    // Escape special characters in description
    const escapedDescription = calendarEvent.description
      .replace(/\\/g, '\\\\')
      .replace(/;/g, '\\;')
      .replace(/,/g, '\\,')
      .replace(/\n/g, '\\n')
    icalContent.push(`DESCRIPTION:${escapedDescription}`)
  }

  if (calendarEvent.location) {
    const escapedLocation = calendarEvent.location
      .replace(/\\/g, '\\\\')
      .replace(/;/g, '\\;')
      .replace(/,/g, '\\,')
    icalContent.push(`LOCATION:${escapedLocation}`)
  }

  if (calendarEvent.url) {
    icalContent.push(`URL:${calendarEvent.url}`)
  }

  icalContent.push('END:VEVENT', 'END:VCALENDAR')

  return icalContent.join('\r\n')
}

/**
 * Generate calendar URL for different services
 */
export function generateCalendarUrl(calendarEvent: CalendarEvent, service: CalendarService): string {
  const startDateTime = formatDateTimeForICal(
    calendarEvent.startDate, 
    calendarEvent.startTime, 
    calendarEvent.timezone
  )
  
  let endDateTime: string
  if (calendarEvent.endDate && calendarEvent.endTime) {
    endDateTime = formatDateTimeForICal(
      calendarEvent.endDate, 
      calendarEvent.endTime, 
      calendarEvent.timezone
    )
  } else {
    // Default to 1 hour duration
    const startDate = new Date(`${calendarEvent.startDate}T${calendarEvent.startTime || '00:00'}`)
    startDate.setHours(startDate.getHours() + 1)
    endDateTime = formatDateTimeForICal(
      startDate.toISOString().split('T')[0],
      startDate.toTimeString().slice(0, 5),
      calendarEvent.timezone
    )
  }

  const params = new URLSearchParams()

  switch (service) {
    case 'google':
      params.set('action', 'TEMPLATE')
      params.set('text', calendarEvent.title)
      params.set('dates', `${startDateTime}/${endDateTime}`)
      if (calendarEvent.description) params.set('details', calendarEvent.description)
      if (calendarEvent.location) params.set('location', calendarEvent.location)
      return `https://calendar.google.com/calendar/render?${params.toString()}`

    case 'outlook':
      params.set('subject', calendarEvent.title)
      params.set('startdt', startDateTime)
      params.set('enddt', endDateTime)
      if (calendarEvent.description) params.set('body', calendarEvent.description)
      if (calendarEvent.location) params.set('location', calendarEvent.location)
      return `https://outlook.live.com/calendar/0/deeplink/compose?${params.toString()}`

    case 'office365':
      params.set('subject', calendarEvent.title)
      params.set('startdt', startDateTime)
      params.set('enddt', endDateTime)
      if (calendarEvent.description) params.set('body', calendarEvent.description)
      if (calendarEvent.location) params.set('location', calendarEvent.location)
      return `https://outlook.office.com/calendar/0/deeplink/compose?${params.toString()}`

    case 'yahoo':
      params.set('v', '60')
      params.set('title', calendarEvent.title)
      params.set('st', startDateTime)
      params.set('et', endDateTime)
      if (calendarEvent.description) params.set('desc', calendarEvent.description)
      if (calendarEvent.location) params.set('in_loc', calendarEvent.location)
      return `https://calendar.yahoo.com/?${params.toString()}`

    default:
      throw new Error(`Unsupported calendar service: ${service}`)
  }
}

/**
 * Download iCal file
 */
export function downloadICalFile(calendarEvent: CalendarEvent, filename?: string): void {
  const icalContent = generateICalContent(calendarEvent)
  const blob = new Blob([icalContent], { type: 'text/calendar;charset=utf-8' })
  
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  
  link.setAttribute('href', url)
  link.setAttribute('download', filename || `${calendarEvent.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.ics`)
  link.style.visibility = 'hidden'
  
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  
  URL.revokeObjectURL(url)
}

/**
 * Open calendar service in new tab
 */
export function openCalendarService(calendarEvent: CalendarEvent, service: CalendarService): void {
  if (service === 'ical') {
    downloadICalFile(calendarEvent)
    return
  }

  try {
    const url = generateCalendarUrl(calendarEvent, service)
    window.open(url, '_blank', 'noopener,noreferrer')
  } catch (error) {
    console.error(`Failed to open ${service} calendar:`, error)
    // Fallback to iCal download
    downloadICalFile(calendarEvent)
  }
}
