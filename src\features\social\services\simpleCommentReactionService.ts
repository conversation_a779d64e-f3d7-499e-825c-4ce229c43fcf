import { supabase } from '@/integrations/supabase/client';

type ReactionType = 'like' | 'support' | 'love' | 'insightful';

export class SimpleCommentReactionService {
  /**
   * Toggle a reaction on a comment (any reaction type)
   */
  static async toggleCommentReaction(commentId: string, reactionType: ReactionType): Promise<boolean> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User must be authenticated to react to comments');
    }

    console.log('Attempting to toggle', reactionType, 'for comment:', commentId, 'user:', user.id);

    try {
      // First, check if user already has this reaction on this comment
      console.log('Checking for existing reaction...');
      const { data: existingReaction, error: selectError } = await supabase
        .from('social_comment_reactions')
        .select('id')
        .eq('comment_id', commentId)
        .eq('user_id', user.id)
        .eq('reaction_type', reactionType)
        .maybeSingle();

      if (selectError) {
        console.error('Error checking existing reaction:', selectError);
        throw new Error(`Failed to check existing reaction: ${selectError.message}`);
      }

      console.log('Existing reaction:', existingReaction);

      if (existingReaction) {
        // Remove the reaction
        const { error: deleteError } = await supabase
          .from('social_comment_reactions')
          .delete()
          .eq('id', existingReaction.id);

        if (deleteError) {
          throw new Error(`Failed to remove ${reactionType}: ${deleteError.message}`);
        }

        return false; // Reaction was removed
      } else {
        // Add the reaction
        const { error: insertError } = await supabase
          .from('social_comment_reactions')
          .insert({
            comment_id: commentId,
            user_id: user.id,
            reaction_type: reactionType
          });

        if (insertError) {
          throw new Error(`Failed to add ${reactionType}: ${insertError.message}`);
        }

        return true; // Reaction was added
      }
    } catch (error) {
      console.error('Error in toggleCommentReaction:', error);
      throw error;
    }
  }

  /**
   * Get user's reactions for a comment
   */
  static async getUserCommentReactions(commentId: string): Promise<ReactionType[]> {
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return [];
    }

    try {
      const { data: reactions, error } = await supabase
        .from('social_comment_reactions')
        .select('reaction_type')
        .eq('comment_id', commentId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error checking user reactions:', error);
        return [];
      }

      return reactions.map(r => r.reaction_type as ReactionType);
    } catch (error) {
      console.error('Error in getUserCommentReactions:', error);
      return [];
    }
  }

  /**
   * Get user's like status for a comment (legacy method)
   */
  static async getUserCommentLike(commentId: string): Promise<boolean> {
    const reactions = await this.getUserCommentReactions(commentId);
    return reactions.includes('like');
  }
}
