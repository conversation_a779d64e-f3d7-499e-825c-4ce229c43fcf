import React, { useState } from 'react'
import { Calendar, ChevronDown, Download, ExternalLink } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useToast } from '@/hooks/use-toast'
import { Event, CalendarService } from '../types'
import { eventToCalendarEvent, openCalendarService } from '../utils/calendarUtils'

interface AddToCalendarDropdownProps {
  event: Event
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  className?: string
}

interface CalendarOption {
  service: CalendarService
  label: string
  icon: string
  description: string
}

const calendarOptions: CalendarOption[] = [
  {
    service: 'google',
    label: 'Google Calendar',
    icon: '📅',
    description: 'Add to Google Calendar'
  },
  {
    service: 'outlook',
    label: 'Outlook Online',
    icon: '📧',
    description: 'Add to Outlook.com'
  },
  {
    service: 'office365',
    label: 'Outlook Office 365',
    icon: '🏢',
    description: 'Add to Office 365'
  },
  {
    service: 'yahoo',
    label: 'Yahoo Calendar',
    icon: '🟣',
    description: 'Add to Yahoo Calendar'
  },
  {
    service: 'ical',
    label: 'Download iCal',
    icon: '💾',
    description: 'Download .ics file'
  }
]

const AddToCalendarDropdown: React.FC<AddToCalendarDropdownProps> = ({
  event,
  variant = 'outline',
  size = 'sm',
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const { toast } = useToast()

  const handleCalendarSelection = (service: CalendarService, label: string) => {
    try {
      const calendarEvent = eventToCalendarEvent(event)
      openCalendarService(calendarEvent, service)
      
      setIsOpen(false)
      
      if (service === 'ical') {
        toast({
          title: "Calendar File Downloaded",
          description: "The event has been downloaded as an iCal file. You can import it into any calendar application.",
          duration: 4000,
        })
      } else {
        toast({
          title: "Opening Calendar",
          description: `Opening ${label} to add this event to your calendar.`,
          duration: 3000,
        })
      }
    } catch (error) {
      console.error('Error adding event to calendar:', error)
      toast({
        title: "Calendar Error",
        description: "There was an issue adding the event to your calendar. Please try downloading the iCal file instead.",
        variant: "destructive",
        duration: 5000,
      })
    }
  }

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button 
          variant={variant} 
          size={size} 
          className={`gap-2 ${className}`}
        >
          <Calendar className="h-4 w-4" />
          Add to Calendar
          <ChevronDown className="h-3 w-3" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <div className="px-2 py-1.5 text-sm font-medium text-muted-foreground">
          Choose your calendar
        </div>
        <DropdownMenuSeparator />
        {calendarOptions.map((option, index) => (
          <React.Fragment key={option.service}>
            <DropdownMenuItem
              onClick={() => handleCalendarSelection(option.service, option.label)}
              className="flex items-center gap-3 cursor-pointer"
            >
              <span className="text-lg">{option.icon}</span>
              <div className="flex-1">
                <div className="font-medium">{option.label}</div>
                <div className="text-xs text-muted-foreground">
                  {option.description}
                </div>
              </div>
              {option.service === 'ical' ? (
                <Download className="h-4 w-4 text-muted-foreground" />
              ) : (
                <ExternalLink className="h-4 w-4 text-muted-foreground" />
              )}
            </DropdownMenuItem>
            {index < calendarOptions.length - 1 && index === calendarOptions.length - 2 && (
              <DropdownMenuSeparator />
            )}
          </React.Fragment>
        ))}
        <DropdownMenuSeparator />
        <div className="px-2 py-1.5 text-xs text-muted-foreground">
          Calendar files work with most calendar apps
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default AddToCalendarDropdown
