import { useNavigate } from 'react-router-dom'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Calendar, 
  Clock, 
  MapPin, 
  Users, 
  Heart, 
  Eye, 
  ExternalLink,
  Globe,
  Building,
  Monitor
} from 'lucide-react'
import type { EventWithInterests } from '../types'
import { EVENT_LOCATION_TYPES } from '../types'
import AddToCalendarDropdown from './AddToCalendarDropdown'

interface EventCardProps {
  event: EventWithInterests
}

const EventCard = ({ event }: EventCardProps) => {
  const navigate = useNavigate()

  // Helper functions
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-GB', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    })
  }

  const formatTime = (timeString: string | null) => {
    if (!timeString) return null
    return timeString.slice(0, 5) // Format HH:MM
  }

  const isEventPast = () => {
    const eventDate = new Date(event.start_date)
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return eventDate < today
  }

  const isEventToday = () => {
    const eventDate = new Date(event.start_date)
    const today = new Date()
    return eventDate.toDateString() === today.toDateString()
  }

  const isEventSoon = () => {
    const eventDate = new Date(event.start_date)
    const today = new Date()
    const diffTime = eventDate.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays <= 7 && diffDays > 0
  }

  const getLocationTypeInfo = () => {
    return EVENT_LOCATION_TYPES.find(type => type.value === event.location_type)
  }

  const getLocationIcon = () => {
    switch (event.location_type) {
      case 'online':
        return <Monitor className="h-3 w-3" />
      case 'face_to_face':
        return <Building className="h-3 w-3" />
      case 'hybrid':
        return <Globe className="h-3 w-3" />
      default:
        return <MapPin className="h-3 w-3" />
    }
  }

  const getLocationText = () => {
    if (event.location_type === 'online') {
      return 'Online Event'
    } else if (event.location_type === 'face_to_face') {
      return event.venue_name || event.city || 'Face to Face'
    } else {
      return event.venue_name || 'Hybrid Event'
    }
  }

  const locationTypeInfo = getLocationTypeInfo()

  return (
    <Card className={`h-full transition-all hover:shadow-md ${isEventPast() ? 'opacity-75' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg leading-tight line-clamp-2">
              {event.name}
            </CardTitle>
            <CardDescription className="mt-1">
              Organized by {event.organizer_name}
            </CardDescription>
          </div>
          
          <div className="flex flex-col gap-2 items-end">
            <Badge 
              variant={isEventPast() ? "secondary" : "default"}
              className="flex items-center gap-1"
            >
              {getLocationIcon()}
              {locationTypeInfo?.label || event.location_type}
            </Badge>
            
            {isEventToday() && (
              <Badge variant="destructive" className="text-xs">
                Today
              </Badge>
            )}
            
            {isEventSoon() && !isEventToday() && (
              <Badge variant="outline" className="text-xs">
                Soon
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Date and Time */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{formatDate(event.start_date)}</span>
            {event.end_date && event.end_date !== event.start_date && (
              <span className="text-muted-foreground">
                - {formatDate(event.end_date)}
              </span>
            )}
          </div>
          
          {(event.start_time || event.end_time) && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>
                {formatTime(event.start_time)}
                {event.end_time && ` - ${formatTime(event.end_time)}`}
                {event.timezone && ` (${event.timezone})`}
              </span>
            </div>
          )}
        </div>

        {/* Location */}
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <MapPin className="h-4 w-4" />
          <span className="truncate">{getLocationText()}</span>
        </div>

        {/* Description */}
        {event.description && (
          <p className="text-sm text-muted-foreground line-clamp-3">
            {event.description}
          </p>
        )}

        {/* Creator Info */}
        {event.creator && (
          <div className="flex items-center gap-2">
            <Avatar className="h-6 w-6">
              <AvatarImage src={event.creator.avatar_url} />
              <AvatarFallback className="text-xs">
                {event.creator.first_name?.[0]}{event.creator.last_name?.[0]}
              </AvatarFallback>
            </Avatar>
            <span className="text-sm text-muted-foreground">
              {event.creator.first_name} {event.creator.last_name}
            </span>
          </div>
        )}

        {/* Interest Stats */}
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Heart className="h-3 w-3" />
            <span>{event.interest_count || 0} interested</span>
          </div>
          
          <div className="flex items-center gap-1">
            <Users className="h-3 w-3" />
            <span>{event.attending_count || 0} attending</span>
          </div>

          {event.max_attendees && (
            <div className="text-xs">
              Max: {event.max_attendees}
            </div>
          )}
        </div>

        {/* Tags */}
        {event.tags && event.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {event.tags.slice(0, 3).map((tag, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {event.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{event.tags.length - 3} more
              </Badge>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="space-y-2 pt-2">
          {/* Primary Action - View Details */}
          <Button
            variant="default"
            size="sm"
            onClick={() => navigate(`/events/${event.id}`)}
            className="w-full flex items-center gap-2"
          >
            <Eye className="h-3 w-3" />
            View Details & Join
          </Button>
          
          {/* Calendar Integration */}
          <div onClick={(e) => e.stopPropagation()}>
            <AddToCalendarDropdown
              event={event}
              variant="outline"
              size="sm"
              className="w-full"
            />
          </div>

          {/* External Links */}
          <div className="flex gap-2">
            {event.registration_url && (
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  window.open(event.registration_url!, '_blank')
                }}
                className="flex-1 flex items-center gap-1"
              >
                <ExternalLink className="h-3 w-3" />
                Register
              </Button>
            )}

            {event.event_url && (
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  window.open(event.event_url!, '_blank')
                }}
                className="flex-1 flex items-center gap-1"
              >
                <ExternalLink className="h-3 w-3" />
                Website
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default EventCard
