import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Heart, ThumbsUp, Lightbulb, HandHeart, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { SimpleCommentReactionService } from '../services/simpleCommentReactionService';

type ReactionType = 'like' | 'support' | 'love' | 'insightful';

interface CommentReactionsProps {
  comment: any; // Simplified for now
  onReactionChange?: (commentId: string) => void;
  className?: string;
}

const reactionConfig = {
  like: {
    icon: ThumbsUp,
    label: 'Like',
    color: 'text-muted-foreground hover:text-blue-600',
    activeColor: 'text-blue-600 bg-blue-50',
    fillColor: '#2563eb', // blue-600
    fillClass: 'force-fill-blue',
    count: (comment: any) => comment.like_count || 0,
    activeMessage: 'You liked this comment',
    removeMessage: 'You removed your like'
  },
  support: {
    icon: HandHeart,
    label: 'Support',
    color: 'text-muted-foreground hover:text-green-600',
    activeColor: 'text-green-600 bg-green-50',
    fillColor: '#16a34a', // green-600
    fillClass: 'force-fill-green',
    count: (comment: any) => comment.support_count || 0,
    activeMessage: 'You showed support for this comment',
    removeMessage: 'You removed your support'
  },
  love: {
    icon: Heart,
    label: 'Love',
    color: 'text-muted-foreground hover:text-red-600',
    activeColor: 'text-red-600 bg-red-50',
    fillColor: '#dc2626', // red-600
    fillClass: 'force-fill-red',
    count: (comment: any) => comment.love_count || 0,
    activeMessage: 'You loved this comment',
    removeMessage: 'You removed your love'
  },
  insightful: {
    icon: Lightbulb,
    label: 'Insightful',
    color: 'text-muted-foreground hover:text-yellow-600',
    activeColor: 'text-yellow-600 bg-yellow-50',
    fillColor: '#ca8a04', // yellow-600
    fillClass: 'force-fill-yellow',
    count: (comment: any) => comment.insightful_count || 0,
    activeMessage: 'You found this comment insightful',
    removeMessage: 'You removed your insightful reaction'
  }
};

const CommentReactions: React.FC<CommentReactionsProps> = ({
  comment,
  onReactionChange,
  className = ''
}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [userReactions, setUserReactions] = useState<ReactionType[]>([]);
  const [loadingReaction, setLoadingReaction] = useState<ReactionType | null>(null);

  // Load initial reaction status when component mounts or user changes
  useEffect(() => {
    const loadReactionStatus = async () => {
      if (user && comment.id) {
        try {
          const reactions = await SimpleCommentReactionService.getUserCommentReactions(comment.id);
          setUserReactions(reactions);
        } catch (error) {
          console.error('Error loading reaction status:', error);
        }
      }
    };

    loadReactionStatus();
  }, [user, comment.id]);

  const handleReactionClick = async (reactionType: ReactionType) => {
    if (!user) {
      toast({
        title: "Sign in required",
        description: "Please sign in to react to comments.",
        variant: "destructive"
      });
      return;
    }

    if (!comment.id) {
      toast({
        title: "Error",
        description: "Comment ID is missing.",
        variant: "destructive"
      });
      return;
    }

    try {
      setLoadingReaction(reactionType);
      
      // Optimistic update
      const wasActive = userReactions.includes(reactionType);
      const newReactions = wasActive
        ? userReactions.filter(r => r !== reactionType)
        : [...userReactions, reactionType];
      
      setUserReactions(newReactions);

      // Make API call
      const actualIsActive = await SimpleCommentReactionService.toggleCommentReaction(comment.id, reactionType);

      // Update with actual result (in case optimistic update was wrong)
      if (actualIsActive !== !wasActive) {
        setUserReactions(
          actualIsActive
            ? [...userReactions.filter(r => r !== reactionType), reactionType]
            : userReactions.filter(r => r !== reactionType)
        );
      }

      // Notify parent component to refresh data
      onReactionChange?.(comment.id);

      const config = reactionConfig[reactionType];
      toast({
        title: actualIsActive ? `${config.label} added!` : `${config.label} removed`,
        description: actualIsActive ? config.activeMessage : config.removeMessage,
      });

    } catch (error) {
      console.error('Error toggling reaction:', error);
      
      // Revert optimistic update on error
      setUserReactions(userReactions);

      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update reaction. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingReaction(null);
    }
  };

  const isReactionActive = (reactionType: ReactionType) => {
    return userReactions.includes(reactionType);
  };

  const getReactionCount = (reactionType: ReactionType) => {
    return reactionConfig[reactionType].count(comment);
  };

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      {(Object.keys(reactionConfig) as ReactionType[]).map(reactionType => {
        const config = reactionConfig[reactionType];
        const Icon = config.icon;
        const count = getReactionCount(reactionType);
        const isActive = isReactionActive(reactionType);
        const isLoading = loadingReaction === reactionType;

        return (
          <Button
            key={reactionType}
            variant="ghost"
            size="sm"
            className={`h-7 px-2 text-xs transition-all duration-200 rounded-full ${
              isActive
                ? `${config.activeColor} ${config.fillClass}`
                : config.color
            }`}
            onClick={() => handleReactionClick(reactionType)}
            disabled={isLoading}
            title={config.label}
          >
            {isLoading ? (
              <Loader2 size={14} className="animate-spin" />
            ) : (
              <Icon
                size={14}
                className={`${count > 0 ? "mr-1" : ""} ${isActive ? 'drop-shadow-sm' : ''}`}
                strokeWidth={isActive ? 3 : 1.5}
                style={{
                  ...(isActive ? { filter: 'brightness(1.1)' } : {}),
                  ...(isActive ? { fill: config.fillColor, stroke: config.fillColor } : {})
                }}
              />
            )}
            {count > 0 && count}
          </Button>
        );
      })}
    </div>
  );
};

export default CommentReactions;
