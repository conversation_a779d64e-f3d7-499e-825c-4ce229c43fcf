import { describe, it, expect } from 'vitest'
import { 
  eventToCalendarEvent, 
  formatDateTimeForICal, 
  generateICalContent, 
  generateCalendarUrl 
} from '../calendarUtils'
import { Event } from '../../types'

// Mock event data for testing
const mockEvent: Event = {
  id: 'test-event-id',
  created_by_user_id: 'user-123',
  name: 'Test Sustainability Workshop',
  description: 'A workshop about sustainable practices in business',
  organizer_name: '<PERSON>',
  organizer_email: '<EMAIL>',
  organizer_phone: '+44 ************',
  start_date: '2024-03-15',
  start_time: '14:30',
  end_date: '2024-03-15',
  end_time: '16:30',
  timezone: 'Europe/London',
  location_type: 'face_to_face',
  venue_name: 'Green Conference Center',
  address_line_1: '123 Eco Street',
  address_line_2: null,
  city: 'London',
  postcode: 'SW1A 1AA',
  online_meeting_url: null,
  online_meeting_details: null,
  max_attendees: 50,
  registration_required: true,
  registration_url: 'https://example.com/register',
  event_url: 'https://example.com/event',
  tags: ['sustainability', 'workshop'],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
}

describe('calendarUtils', () => {
  describe('eventToCalendarEvent', () => {
    it('should convert face-to-face event correctly', () => {
      const calendarEvent = eventToCalendarEvent(mockEvent)
      
      expect(calendarEvent.title).toBe('Test Sustainability Workshop')
      expect(calendarEvent.description).toBe('A workshop about sustainable practices in business')
      expect(calendarEvent.startDate).toBe('2024-03-15')
      expect(calendarEvent.startTime).toBe('14:30')
      expect(calendarEvent.endDate).toBe('2024-03-15')
      expect(calendarEvent.endTime).toBe('16:30')
      expect(calendarEvent.timezone).toBe('Europe/London')
      expect(calendarEvent.location).toBe('Green Conference Center, 123 Eco Street, London, SW1A 1AA')
      expect(calendarEvent.url).toBe('https://example.com/event')
    })

    it('should handle online events correctly', () => {
      const onlineEvent = {
        ...mockEvent,
        location_type: 'online' as const,
        online_meeting_url: 'https://zoom.us/j/123456789',
        venue_name: null,
        address_line_1: null,
        city: null,
        postcode: null
      }
      
      const calendarEvent = eventToCalendarEvent(onlineEvent)
      expect(calendarEvent.location).toBe('https://zoom.us/j/123456789')
    })

    it('should handle hybrid events correctly', () => {
      const hybridEvent = {
        ...mockEvent,
        location_type: 'hybrid' as const,
        online_meeting_url: 'https://zoom.us/j/123456789'
      }
      
      const calendarEvent = eventToCalendarEvent(hybridEvent)
      expect(calendarEvent.location).toBe('Green Conference Center, 123 Eco Street, London, SW1A 1AA | Online: https://zoom.us/j/123456789')
    })
  })

  describe('formatDateTimeForICal', () => {
    it('should format date and time correctly for iCal', () => {
      const formatted = formatDateTimeForICal('2024-03-15', '14:30', 'Europe/London')
      // The exact format depends on timezone conversion, but should be in YYYYMMDDTHHMMSSZ format
      expect(formatted).toMatch(/^\d{8}T\d{6}Z$/)
    })

    it('should handle date without time', () => {
      const formatted = formatDateTimeForICal('2024-03-15', undefined, 'Europe/London')
      expect(formatted).toMatch(/^\d{8}T\d{6}Z$/)
    })
  })

  describe('generateICalContent', () => {
    it('should generate valid iCal content', () => {
      const calendarEvent = eventToCalendarEvent(mockEvent)
      const icalContent = generateICalContent(calendarEvent)
      
      expect(icalContent).toContain('BEGIN:VCALENDAR')
      expect(icalContent).toContain('END:VCALENDAR')
      expect(icalContent).toContain('BEGIN:VEVENT')
      expect(icalContent).toContain('END:VEVENT')
      expect(icalContent).toContain('SUMMARY:Test Sustainability Workshop')
      expect(icalContent).toContain('DESCRIPTION:A workshop about sustainable practices in business')
      expect(icalContent).toContain('LOCATION:Green Conference Center, 123 Eco Street, London, SW1A 1AA')
    })
  })

  describe('generateCalendarUrl', () => {
    it('should generate Google Calendar URL correctly', () => {
      const calendarEvent = eventToCalendarEvent(mockEvent)
      const url = generateCalendarUrl(calendarEvent, 'google')
      
      expect(url).toContain('calendar.google.com')
      expect(url).toContain('action=TEMPLATE')
      expect(url).toContain('text=Test%20Sustainability%20Workshop')
    })

    it('should generate Outlook URL correctly', () => {
      const calendarEvent = eventToCalendarEvent(mockEvent)
      const url = generateCalendarUrl(calendarEvent, 'outlook')
      
      expect(url).toContain('outlook.live.com')
      expect(url).toContain('subject=Test%20Sustainability%20Workshop')
    })

    it('should generate Yahoo Calendar URL correctly', () => {
      const calendarEvent = eventToCalendarEvent(mockEvent)
      const url = generateCalendarUrl(calendarEvent, 'yahoo')
      
      expect(url).toContain('calendar.yahoo.com')
      expect(url).toContain('title=Test%20Sustainability%20Workshop')
    })

    it('should throw error for unsupported service', () => {
      const calendarEvent = eventToCalendarEvent(mockEvent)
      expect(() => {
        generateCalendarUrl(calendarEvent, 'unsupported' as any)
      }).toThrow('Unsupported calendar service: unsupported')
    })
  })
})
