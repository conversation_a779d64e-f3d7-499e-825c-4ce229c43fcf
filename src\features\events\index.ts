// Events Directory feature exports
export { default as EventsDirectoryPage } from './pages/EventsDirectoryPage';
export { default as EventDetailPage } from './pages/EventDetailPage';
export { default as EventManagementPage } from './pages/EventManagementPage';

// Export components
export { default as AddEventForm } from './components/AddEventForm';
export { default as EventCard } from './components/EventCard';
export { default as EventManagement } from './components/EventManagement';
export { default as AddToCalendarDropdown } from './components/AddToCalendarDropdown';

// Export services
export { EventService } from './services/eventService';

// Export utilities
export * from './utils/calendarUtils';

// Export types
export type {
  Event,
  NewEvent,
  EventInterest,
  NewEventInterest,
  EventWithInterests,
  EventFormData,
  EventInterestFormData,
  EventFilters,
  EventSortOptions,
  EventListResponse,
  EventLocationType,
  AttendeeProfile,
  GDPRConsentData,
  CalendarEvent,
  CalendarService
} from './types';
